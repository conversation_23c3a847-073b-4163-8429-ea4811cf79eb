//+------------------------------------------------------------------+
//|                                                    BreakoutEA.mq4 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      ""
#property version   "1.00"
#property strict

// 输入参数
input int    LookbackBars = 10;        // 回看柱数
input int    MinRange = 2500;          // 最小波动范围(点)
input int    BreakoutDistance = 100;   // 突破距离(点)
input int    StopLossBars = 3;         // 止损参考柱数
input int    StopLossOffset = 100;     // 止损偏移(点)
input int    TakeProfit = 2000;        // 止盈(点)
input int    TrailingProfit = 200;     // 移动止损盈利触发(点，0=不限制)
input int    TrailingOffset = 50;      // 移动止损偏移(点)
input bool   EnableTrailing = true;    // 启用移动止损
input bool   EnableRecovery = true;    // 启用止损触发回本功能
input int    RecoveryStopBars = 3;     // 回本单止损参考柱数
input bool   EnableRecoveryTP = true;  // 启用回本单止盈
input double LotSize = 0.1;            // 手数

// 全局变量
int magicNumber = 12345;
int recoveryMagicNumber = 54321;  // 回本单魔术号码
datetime lastBarTime = 0;

// 策略单和回本单跟踪
struct OrderInfo
{
   int ticket;
   double openPrice;
   double lotSize;
   int orderType;
   bool hasRecoveryOrder;
   int recoveryTicket;
};

OrderInfo strategyOrder;  // 当前策略单信息

int OnInit()
{
   Print("突破EA初始化完成");

   // 初始化策略单信息
   strategyOrder.ticket = -1;
   strategyOrder.openPrice = 0;
   strategyOrder.lotSize = 0;
   strategyOrder.orderType = -1;
   strategyOrder.hasRecoveryOrder = false;
   strategyOrder.recoveryTicket = -1;

   return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason)
{
   Print("突破EA已停止");
}

void OnTick()
{
   // 实时检测回本功能
   if(EnableRecovery) CheckRecoveryTrigger();

   if(HasOpenPosition())
   {
      if(EnableTrailing) HandleTrailingStop();
      return;
   }

   AnalyzeBreakout();
}

bool IsNewBar()
{
   datetime currentBarTime = Time[0];
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      return true;
   }
   return false;
}

bool HasOpenPosition()
{
   return HasStrategyPosition() || HasRecoveryPosition();
}

bool HasStrategyPosition()
{
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magicNumber)
         {
            // 更新策略单信息
            strategyOrder.ticket = OrderTicket();
            strategyOrder.openPrice = OrderOpenPrice();
            strategyOrder.lotSize = OrderLots();
            strategyOrder.orderType = OrderType();
            return true;
         }
      }
   }

   // 如果没有找到策略单，重置信息
   if(strategyOrder.ticket != -1)
   {
      strategyOrder.ticket = -1;
      strategyOrder.hasRecoveryOrder = false;
      strategyOrder.recoveryTicket = -1;
   }

   return false;
}

bool HasRecoveryPosition()
{
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == recoveryMagicNumber)
         {
            return true;
         }
      }
   }
   return false;
}

void AnalyzeBreakout()
{
   double highPrice = GetHighestHigh(LookbackBars);
   double lowPrice = GetLowestLow(LookbackBars);
   double currentPrice = Ask;

   double range = (highPrice - lowPrice) / Point;

   if(range < MinRange)
   {
      // 检查前一柱K线类型
      bool isPreviousBullish = Close[1] > Open[1];  // 前一柱是阳线
      bool isPreviousBearish = Close[1] < Open[1];  // 前一柱是阴线

      if(currentPrice > highPrice + BreakoutDistance * Point && isPreviousBullish)
      {
         OpenBuyOrder(lowPrice);
      }
      else if(currentPrice < lowPrice - BreakoutDistance * Point && isPreviousBearish)
      {
         OpenSellOrder(highPrice);
      }
   }
}

double GetHighestHigh(int bars)
{
   double highest = High[4];
   for(int i = 4; i <= bars; i++)
   {
      if(High[i] > highest)
         highest = High[i];
   }
   return highest;
}

double GetLowestLow(int bars)
{
   double lowest = Low[4];
   for(int i = 4; i <= bars; i++)
   {
      if(Low[i] < lowest)
         lowest = Low[i];
   }
   return lowest;
}

void OpenBuyOrder(double stopLossPrice)
{
   double stopLoss = GetLowestLow(StopLossBars) - StopLossOffset * Point;
   double takeProfit = Ask + TakeProfit * Point;
   
   int ticket = OrderSend(Symbol(), OP_BUY, LotSize, Ask, 3, stopLoss, takeProfit, 
                         "Breakout Buy", magicNumber, 0, clrGreen);
   
   if(ticket > 0)
   {
      Print("多单开仓成功，订单号: ", ticket, " (前一柱为阳线)");
   }
   else
   {
      Print("多单开仓失败，错误代码: ", GetLastError());
   }
}

void OpenSellOrder(double stopLossPrice)
{
   double stopLoss = GetHighestHigh(StopLossBars) + StopLossOffset * Point;
   double takeProfit = Bid - TakeProfit * Point;
   
   int ticket = OrderSend(Symbol(), OP_SELL, LotSize, Bid, 3, stopLoss, takeProfit, 
                         "Breakout Sell", magicNumber, 0, clrRed);
   
   if(ticket > 0)
   {
      Print("空单开仓成功，订单号: ", ticket, " (前一柱为阴线)");
   }
   else
   {
      Print("空单开仓失败，错误代码: ", GetLastError());
   }
}

void HandleTrailingStop()
{
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magicNumber)
         {
            if(OrderType() == OP_BUY)
            {
               double previousLow = Low[1];
               double openPrice = OrderOpenPrice();
               double triggerProfit = previousLow - openPrice;

               // 当TrailingProfit=0时不受盈利限制，否则检查盈利是否达到触发点
               bool canTrail = (TrailingProfit == 0) || (triggerProfit > TrailingProfit * Point);

               if(canTrail)
               {
                  double newStopLoss = previousLow - TrailingOffset * Point;
                  if(newStopLoss > OrderStopLoss())
                  {
                     OrderModify(OrderTicket(), openPrice, newStopLoss,
                                OrderTakeProfit(), 0, clrBlue);
                     Print("多单移动止损: 新止损=", DoubleToStr(newStopLoss, Digits));
                  }
               }
            }
            else if(OrderType() == OP_SELL)
            {
               double previousHigh = High[1];
               double openPrice = OrderOpenPrice();
               double triggerProfit = openPrice - previousHigh;

               // 当TrailingProfit=0时不受盈利限制，否则检查盈利是否达到触发点
               bool canTrail = (TrailingProfit == 0) || (triggerProfit > TrailingProfit * Point);

               if(canTrail)
               {
                  double newStopLoss = previousHigh + TrailingOffset * Point;
                  if(newStopLoss < OrderStopLoss() || OrderStopLoss() == 0)
                  {
                     OrderModify(OrderTicket(), openPrice, newStopLoss,
                                OrderTakeProfit(), 0, clrBlue);
                     Print("空单移动止损: 新止损=", DoubleToStr(newStopLoss, Digits));
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 检测回本触发条件                                                 |
//+------------------------------------------------------------------+
void CheckRecoveryTrigger()
{
   // 检查是否有策略单且还没有回本单
   if(strategyOrder.ticket == -1 || strategyOrder.hasRecoveryOrder) return;

   // 检查策略单是否已经被止损平仓
   if(!OrderSelect(strategyOrder.ticket, SELECT_BY_TICKET, MODE_HISTORY)) return;

   // 确认订单已关闭且是亏损的
   if(OrderCloseTime() > 0)
   {
      double profit = OrderProfit() + OrderSwap() + OrderCommission();
      if(profit < 0) // 策略单亏损平仓
      {
         Print("策略单亏损平仓，触发回本功能。亏损金额: ", DoubleToStr(profit, 2));
         OpenRecoveryOrder();
      }
   }
}

//+------------------------------------------------------------------+
//| 开启回本单                                                       |
//+------------------------------------------------------------------+
void OpenRecoveryOrder()
{
   if(strategyOrder.hasRecoveryOrder) return; // 已经有回本单了

   // 计算回本单参数
   int recoveryType = (strategyOrder.orderType == OP_BUY) ? OP_SELL : OP_BUY;
   double recoveryLots = strategyOrder.lotSize;

   double stopLoss, takeProfit;

   if(recoveryType == OP_BUY)
   {
      // 回本多单：止损为前N柱最低点
      stopLoss = GetLowestLow(RecoveryStopBars);

      if(EnableRecoveryTP)
      {
         // 计算策略单的亏损点数
         double lossPoints = MathAbs(strategyOrder.openPrice - OrderClosePrice()) / Point;
         takeProfit = Ask + lossPoints * Point;
      }
      else
      {
         takeProfit = 0; // 不设置止盈
      }

      int ticket = OrderSend(Symbol(), OP_BUY, recoveryLots, Ask, 3, stopLoss, takeProfit,
                            "回本多单", recoveryMagicNumber, 0, clrBlue);

      if(ticket > 0)
      {
         strategyOrder.hasRecoveryOrder = true;
         strategyOrder.recoveryTicket = ticket;
         Print("回本多单开仓成功，订单号: ", ticket, " 手数: ", DoubleToStr(recoveryLots, 2));
      }
   }
   else if(recoveryType == OP_SELL)
   {
      // 回本空单：止损为前N柱最高点
      stopLoss = GetHighestHigh(RecoveryStopBars);

      if(EnableRecoveryTP)
      {
         // 计算策略单的亏损点数
         double lossPoints = MathAbs(strategyOrder.openPrice - OrderClosePrice()) / Point;
         takeProfit = Bid - lossPoints * Point;
      }
      else
      {
         takeProfit = 0; // 不设置止盈
      }

      int ticket = OrderSend(Symbol(), OP_SELL, recoveryLots, Bid, 3, stopLoss, takeProfit,
                            "回本空单", recoveryMagicNumber, 0, clrBlue);

      if(ticket > 0)
      {
         strategyOrder.hasRecoveryOrder = true;
         strategyOrder.recoveryTicket = ticket;
         Print("回本空单开仓成功，订单号: ", ticket, " 手数: ", DoubleToStr(recoveryLots, 2));
      }
   }
}