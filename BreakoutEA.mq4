//+------------------------------------------------------------------+
//|                                                    BreakoutEA.mq4 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      ""
#property version   "1.00"
#property strict

// 输入参数
input int    LookbackBars = 10;        // 回看柱数
input int    MinRange = 2500;          // 最小波动范围(点)
input int    BreakoutDistance = 100;   // 突破距离(点)
input int    StopLossBars = 3;         // 止损参考柱数
input int    StopLossOffset = 100;     // 止损偏移(点)
input int    TakeProfit = 2000;        // 止盈(点)
input int    TrailingProfit = 200;     // 移动止损盈利触发(点，0=不限制)
input int    TrailingOffset = 50;      // 移动止损偏移(点)
input bool   EnableTrailing = true;    // 启用移动止损
input double LotSize = 0.1;            // 手数

// 全局变量
int magicNumber = 12345;
datetime lastBarTime = 0;

int OnInit()
{
   Print("突破EA初始化完成");
   return(INIT_SUCCEEDED);
}

void OnDeinit(const int reason)
{
   Print("突破EA已停止");
}

void OnTick()
{
   if(HasOpenPosition()) 
   {
      if(EnableTrailing) HandleTrailingStop();
      return;
   }
   
   AnalyzeBreakout();
}

bool IsNewBar()
{
   datetime currentBarTime = Time[0];
   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      return true;
   }
   return false;
}

bool HasOpenPosition()
{
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magicNumber)
         {
            return true;
         }
      }
   }
   return false;
}

void AnalyzeBreakout()
{
   double highPrice = GetHighestHigh(LookbackBars);
   double lowPrice = GetLowestLow(LookbackBars);
   double currentPrice = Ask;

   double range = (highPrice - lowPrice) / Point;

   if(range < MinRange)
   {
      // 检查前一柱K线类型
      bool isPreviousBullish = Close[1] > Open[1];  // 前一柱是阳线
      bool isPreviousBearish = Close[1] < Open[1];  // 前一柱是阴线

      if(currentPrice > highPrice + BreakoutDistance * Point && isPreviousBullish)
      {
         OpenBuyOrder(lowPrice);
      }
      else if(currentPrice < lowPrice - BreakoutDistance * Point && isPreviousBearish)
      {
         OpenSellOrder(highPrice);
      }
   }
}

double GetHighestHigh(int bars)
{
   double highest = High[4];
   for(int i = 4; i <= bars; i++)
   {
      if(High[i] > highest)
         highest = High[i];
   }
   return highest;
}

double GetLowestLow(int bars)
{
   double lowest = Low[4];
   for(int i = 4; i <= bars; i++)
   {
      if(Low[i] < lowest)
         lowest = Low[i];
   }
   return lowest;
}

void OpenBuyOrder(double stopLossPrice)
{
   double stopLoss = GetLowestLow(StopLossBars) - StopLossOffset * Point;
   double takeProfit = Ask + TakeProfit * Point;
   
   int ticket = OrderSend(Symbol(), OP_BUY, LotSize, Ask, 3, stopLoss, takeProfit, 
                         "Breakout Buy", magicNumber, 0, clrGreen);
   
   if(ticket > 0)
   {
      Print("多单开仓成功，订单号: ", ticket);
   }
   else
   {
      Print("多单开仓失败，错误代码: ", GetLastError());
   }
}

void OpenSellOrder(double stopLossPrice)
{
   double stopLoss = GetHighestHigh(StopLossBars) + StopLossOffset * Point;
   double takeProfit = Bid - TakeProfit * Point;
   
   int ticket = OrderSend(Symbol(), OP_SELL, LotSize, Bid, 3, stopLoss, takeProfit, 
                         "Breakout Sell", magicNumber, 0, clrRed);
   
   if(ticket > 0)
   {
      Print("空单开仓成功，订单号: ", ticket);
   }
   else
   {
      Print("空单开仓失败，错误代码: ", GetLastError());
   }
}

void HandleTrailingStop()
{
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magicNumber)
         {
            if(OrderType() == OP_BUY)
            {
               double previousLow = Low[1];
               double openPrice = OrderOpenPrice();
               double triggerProfit = previousLow - openPrice;

               // 当TrailingProfit=0时不受盈利限制，否则检查盈利是否达到触发点
               bool canTrail = (TrailingProfit == 0) || (triggerProfit > TrailingProfit * Point);

               if(canTrail)
               {
                  double newStopLoss = previousLow - TrailingOffset * Point;
                  if(newStopLoss > OrderStopLoss())
                  {
                     OrderModify(OrderTicket(), openPrice, newStopLoss,
                                OrderTakeProfit(), 0, clrBlue);
                     Print("多单移动止损: 新止损=", DoubleToStr(newStopLoss, Digits));
                  }
               }
            }
            else if(OrderType() == OP_SELL)
            {
               double previousHigh = High[1];
               double openPrice = OrderOpenPrice();
               double triggerProfit = openPrice - previousHigh;

               // 当TrailingProfit=0时不受盈利限制，否则检查盈利是否达到触发点
               bool canTrail = (TrailingProfit == 0) || (triggerProfit > TrailingProfit * Point);

               if(canTrail)
               {
                  double newStopLoss = previousHigh + TrailingOffset * Point;
                  if(newStopLoss < OrderStopLoss() || OrderStopLoss() == 0)
                  {
                     OrderModify(OrderTicket(), openPrice, newStopLoss,
                                OrderTakeProfit(), 0, clrBlue);
                     Print("空单移动止损: 新止损=", DoubleToStr(newStopLoss, Digits));
                  }
               }
            }
         }
      }
   }
} 